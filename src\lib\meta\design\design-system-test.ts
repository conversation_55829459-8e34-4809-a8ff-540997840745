/**
 * Design System Validation Tests
 * 
 * This file contains validation tests to ensure the unified design system
 * works correctly and maintains all expected functionality.
 */

import { 
  unifiedContractDesign, 
  createCustomDesign, 
  createStyleSheet 
} from './unified-contract-template';

import { 
  ContractDesignBuilder, 
  createDesignBuilder, 
  quickCustomDesign 
} from './section-design-builder';

// ============================================================================
// BASIC FUNCTIONALITY TESTS
// ============================================================================

/**
 * Test 1: Verify default design system structure
 */
export function testDefaultDesignStructure(): boolean {
  try {
    // Check that all required sections exist
    const requiredSections = [
      'page', 'header', 'partyIdentity', 'workLocation', 
      'employmentTerms', 'workTimeSalary', 'standardSections',
      'legalReference', 'signature', 'footer', 'foundation'
    ];

    for (const section of requiredSections) {
      if (!(section in unifiedContractDesign)) {
        console.error(`Missing section: ${section}`);
        return false;
      }
    }

    // Check that foundation tokens exist
    const foundation = unifiedContractDesign.foundation;
    if (!foundation.brand || !foundation.typography || !foundation.spacing) {
      console.error('Missing foundation tokens');
      return false;
    }

    console.log('✅ Default design structure test passed');
    return true;
  } catch (error) {
    console.error('❌ Default design structure test failed:', error);
    return false;
  }
}

/**
 * Test 2: Verify StyleSheet creation
 */
export function testStyleSheetCreation(): boolean {
  try {
    const styleSheet = createStyleSheet();
    
    // Check that essential styles exist
    const requiredStyles = [
      'page', 'header', 'companyName', 'title', 'partySection',
      'workSection', 'employmentSection', 'salarySection',
      'standardSection', 'signatureSection', 'legalText'
    ];

    for (const style of requiredStyles) {
      if (!(style in styleSheet)) {
        console.error(`Missing style: ${style}`);
        return false;
      }
    }

    console.log('✅ StyleSheet creation test passed');
    return true;
  } catch (error) {
    console.error('❌ StyleSheet creation test failed:', error);
    return false;
  }
}

/**
 * Test 3: Verify design builder functionality
 */
export function testDesignBuilder(): boolean {
  try {
    const customDesign = new ContractDesignBuilder()
      .header()
        .companyName({ fontSize: 20, color: '#3b82f6' })
        .contractTitle({ fontSize: 22 })
      .done()
      .signature()
        .signatureLine({ borderBottomColor: '#3b82f6' })
      .done()
      .build();

    // Verify customizations were applied
    if (customDesign.header.companyName.fontSize !== 20) {
      console.error('Header customization not applied');
      return false;
    }

    if (customDesign.signature.signatureLine.borderBottomColor !== '#3b82f6') {
      console.error('Signature customization not applied');
      return false;
    }

    console.log('✅ Design builder test passed');
    return true;
  } catch (error) {
    console.error('❌ Design builder test failed:', error);
    return false;
  }
}

/**
 * Test 4: Verify preset functionality
 */
export function testPresets(): boolean {
  try {
    const presets = ['professional', 'modern', 'minimal'] as const;
    
    for (const preset of presets) {
      const design = createDesignBuilder(preset).build();
      
      // Verify design was created
      if (!design || !design.header || !design.signature) {
        console.error(`Preset ${preset} failed to create valid design`);
        return false;
      }
    }

    console.log('✅ Presets test passed');
    return true;
  } catch (error) {
    console.error('❌ Presets test failed:', error);
    return false;
  }
}

/**
 * Test 5: Verify quick customization
 */
export function testQuickCustomization(): boolean {
  try {
    const design = quickCustomDesign({
      header: {
        companyName: { fontSize: 24, color: '#059669' }
      },
      signature: {
        signatureLine: { borderBottomColor: '#059669' }
      }
    });

    // Verify customizations were applied
    if (design.header.companyName.fontSize !== 24) {
      console.error('Quick customization not applied to header');
      return false;
    }

    if (design.signature.signatureLine.borderBottomColor !== '#059669') {
      console.error('Quick customization not applied to signature');
      return false;
    }

    console.log('✅ Quick customization test passed');
    return true;
  } catch (error) {
    console.error('❌ Quick customization test failed:', error);
    return false;
  }
}

/**
 * Test 6: Verify section isolation
 */
export function testSectionIsolation(): boolean {
  try {
    const originalDesign = unifiedContractDesign;
    
    // Customize only header
    const customDesign = new ContractDesignBuilder()
      .header()
        .companyName({ fontSize: 30 })
      .done()
      .build();

    // Verify header was changed
    if (customDesign.header.companyName.fontSize !== 30) {
      console.error('Header customization not applied');
      return false;
    }

    // Verify other sections remain unchanged
    if (customDesign.signature.signatureLine.borderBottomColor !== 
        originalDesign.signature.signatureLine.borderBottomColor) {
      console.error('Section isolation failed - signature was affected');
      return false;
    }

    if (customDesign.partyIdentity.sectionTitle.fontSize !== 
        originalDesign.partyIdentity.sectionTitle.fontSize) {
      console.error('Section isolation failed - party identity was affected');
      return false;
    }

    console.log('✅ Section isolation test passed');
    return true;
  } catch (error) {
    console.error('❌ Section isolation test failed:', error);
    return false;
  }
}

// ============================================================================
// COMPREHENSIVE TEST RUNNER
// ============================================================================

/**
 * Run all validation tests
 */
export function runAllTests(): boolean {
  console.log('🧪 Running unified design system validation tests...\n');

  const tests = [
    { name: 'Default Design Structure', test: testDefaultDesignStructure },
    { name: 'StyleSheet Creation', test: testStyleSheetCreation },
    { name: 'Design Builder', test: testDesignBuilder },
    { name: 'Presets', test: testPresets },
    { name: 'Quick Customization', test: testQuickCustomization },
    { name: 'Section Isolation', test: testSectionIsolation }
  ];

  let passedTests = 0;
  let totalTests = tests.length;

  for (const { name, test } of tests) {
    console.log(`\n🔍 Testing: ${name}`);
    if (test()) {
      passedTests++;
    }
  }

  console.log(`\n📊 Test Results: ${passedTests}/${totalTests} tests passed`);

  if (passedTests === totalTests) {
    console.log('🎉 All tests passed! The unified design system is working correctly.');
    return true;
  } else {
    console.log('❌ Some tests failed. Please check the implementation.');
    return false;
  }
}

/**
 * Quick validation function for development
 */
export function validateDesignSystem(): void {
  try {
    // Quick smoke test
    const design = new ContractDesignBuilder()
      .header()
        .companyName({ fontSize: 20 })
      .done()
      .build();

    const styleSheet = createStyleSheet(design);

    console.log('✅ Design system validation passed');
    console.log('📋 Available sections:', Object.keys(unifiedContractDesign));
    console.log('🎨 Generated styles count:', Object.keys(styleSheet).length);
  } catch (error) {
    console.error('❌ Design system validation failed:', error);
  }
}

// ============================================================================
// USAGE EXAMPLES FOR TESTING
// ============================================================================

/**
 * Example: Test different customization approaches
 */
export function testCustomizationApproaches(): void {
  console.log('🎨 Testing different customization approaches...\n');

  // Approach 1: Fluent API
  console.log('1. Fluent API approach:');
  const fluentDesign = new ContractDesignBuilder()
    .header()
      .companyName({ fontSize: 20, color: '#3b82f6' })
    .done()
    .build();
  console.log('   Header company name fontSize:', fluentDesign.header.companyName.fontSize);

  // Approach 2: Preset with overrides
  console.log('\n2. Preset with overrides:');
  const presetDesign = createDesignBuilder('modern')
    .header()
      .companyName({ fontSize: 22 })
    .done()
    .build();
  console.log('   Header company name fontSize:', presetDesign.header.companyName.fontSize);

  // Approach 3: Quick customization
  console.log('\n3. Quick customization:');
  const quickDesign = quickCustomDesign({
    header: {
      companyName: { fontSize: 24 }
    }
  });
  console.log('   Header company name fontSize:', quickDesign.header.companyName.fontSize);

  console.log('\n✅ All customization approaches working correctly!');
}
