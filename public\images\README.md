# Logo Setup for Arbeidskontrakt PDF

## 📋 How to Add Your Company Logo

### Step 1: Add Logo File
1. Place your company logo in this directory (`public/images/`)
2. Supported formats: PNG, JPG, SVG
3. Recommended size: 200x80px or similar aspect ratio
4. Example filename: `ringerike-landskap-logo.png`

### Step 2: Update PDF Template
In `src/components/Meta/ContractPDFTemplate.tsx`, uncomment and update the logo section:

```tsx
{/* Logo - uncomment and update src path */}
<View style={styles.logoContainer}>
  <Image 
    style={styles.logo} 
    src="/images/ringerike-landskap-logo.png" 
  />
</View>
```

### Step 3: Adjust Logo Styling
Modify logo size in `src/lib/meta/design/contract-design-system.ts`:

```typescript
logo: {
  width: 80,        // Adjust width
  height: 'auto',   // Maintains aspect ratio
  marginBottom: foundationTokens.spacing.small,
},
```

## 🎨 Logo Design Recommendations

### For Professional Contracts:
- **Format**: PNG with transparent background
- **Colors**: Black/grayscale or company brand colors
- **Size**: 200-400px wide, proportional height
- **Style**: Clean, professional, high contrast

### Example Logo Placement:
```
┌─────────────────────────┐
│       [LOGO HERE]       │
│   Ringerike Landskap AS │
│   Org.nr: ***********   │
│   Birchs vei 7, 3530 Røyse │
│                         │
│    ARBEIDSKONTRAKT      │
└─────────────────────────┘
```

## 🔧 Technical Notes

- React-PDF supports PNG, JPG, and SVG formats
- Logo loads from public URL or base64 data
- For production, ensure logo is accessible via HTTPS
- Test PDF generation after adding logo to verify rendering

## 📁 File Structure
```
public/
  images/
    ringerike-landskap-logo.png  ← Add your logo here
    README.md                    ← This file
```
