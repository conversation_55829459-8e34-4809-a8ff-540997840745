/**
 * Section-Based Design Builder for Arbeidskontrakt PDF Generator
 * 
 * This module provides an intuitive, fluent interface for customizing PDF design
 * on a section-by-section basis. It eliminates the need to navigate across files
 * and enables incremental, isolated customization of each contract section.
 * 
 * Key Features:
 * - Fluent API for chaining section customizations
 * - Type-safe design modifications with IntelliSense support
 * - Section isolation - changes to one section don't affect others
 * - Preset design variants (professional, modern, minimal)
 * - Runtime design validation and error checking
 * - Zero configuration drift through centralized management
 */

import { 
  unifiedContractDesign, 
  createCustomDesign, 
  createStyleSheet,
  UnifiedContractDesign,
  ContractSection,
  designFoundation
} from './unified-contract-template';

// ============================================================================
// DESIGN BUILDER CLASS
// ============================================================================

/**
 * Fluent design builder for systematic section-by-section customization
 * 
 * @example
 * ```typescript
 * const customDesign = new ContractDesignBuilder()
 *   .header()
 *     .companyName({ fontSize: 20, color: '#3b82f6' })
 *     .contractTitle({ fontSize: 22 })
 *   .signature()
 *     .signatureLine({ borderBottomColor: '#3b82f6' })
 *   .build();
 * ```
 */
export class ContractDesignBuilder {
  private designOverrides: Partial<Record<ContractSection, any>> = {};
  private currentSection: ContractSection | null = null;

  /**
   * Start customizing the header section
   */
  header(): HeaderSectionBuilder {
    this.currentSection = 'header';
    return new HeaderSectionBuilder(this);
  }

  /**
   * Start customizing the party identity section
   */
  partyIdentity(): PartyIdentitySectionBuilder {
    this.currentSection = 'partyIdentity';
    return new PartyIdentitySectionBuilder(this);
  }

  /**
   * Start customizing the work location section
   */
  workLocation(): WorkLocationSectionBuilder {
    this.currentSection = 'workLocation';
    return new WorkLocationSectionBuilder(this);
  }

  /**
   * Start customizing the employment terms section
   */
  employmentTerms(): EmploymentTermsSectionBuilder {
    this.currentSection = 'employmentTerms';
    return new EmploymentTermsSectionBuilder(this);
  }

  /**
   * Start customizing the work time & salary section
   */
  workTimeSalary(): WorkTimeSalarySectionBuilder {
    this.currentSection = 'workTimeSalary';
    return new WorkTimeSalarySectionBuilder(this);
  }

  /**
   * Start customizing the standard sections (vacation, termination, etc.)
   */
  standardSections(): StandardSectionBuilder {
    this.currentSection = 'standardSections';
    return new StandardSectionBuilder(this);
  }

  /**
   * Start customizing the legal reference section
   */
  legalReference(): LegalReferenceSectionBuilder {
    this.currentSection = 'legalReference';
    return new LegalReferenceSectionBuilder(this);
  }

  /**
   * Start customizing the signature section
   */
  signature(): SignatureSectionBuilder {
    this.currentSection = 'signature';
    return new SignatureSectionBuilder(this);
  }

  /**
   * Start customizing the page footer section
   */
  footer(): FooterSectionBuilder {
    this.currentSection = 'footer';
    return new FooterSectionBuilder(this);
  }

  /**
   * Start customizing the page layout
   */
  page(): PageSectionBuilder {
    this.currentSection = 'page';
    return new PageSectionBuilder(this);
  }

  /**
   * Apply a design preset (professional, modern, minimal)
   */
  preset(presetName: 'professional' | 'modern' | 'minimal'): this {
    const presets = getDesignPresets();
    const preset = presets[presetName];
    if (preset) {
      this.designOverrides = { ...this.designOverrides, ...preset };
    }
    return this;
  }

  /**
   * Apply custom overrides directly
   */
  custom(overrides: Partial<Record<ContractSection, any>>): this {
    this.designOverrides = { ...this.designOverrides, ...overrides };
    return this;
  }

  /**
   * Internal method to add section overrides
   */
  _addSectionOverride(section: ContractSection, elementKey: string, styles: any): this {
    if (!this.designOverrides[section]) {
      this.designOverrides[section] = {};
    }
    this.designOverrides[section][elementKey] = {
      ...this.designOverrides[section][elementKey],
      ...styles
    };
    return this;
  }

  /**
   * Build the final design configuration
   */
  build(): UnifiedContractDesign {
    return createCustomDesign(this.designOverrides);
  }

  /**
   * Build and create React-PDF StyleSheet
   */
  buildStyleSheet() {
    const design = this.build();
    return createStyleSheet(design);
  }

  /**
   * Reset all customizations
   */
  reset(): this {
    this.designOverrides = {};
    this.currentSection = null;
    return this;
  }

  /**
   * Get current design overrides for debugging
   */
  getOverrides(): Partial<Record<ContractSection, any>> {
    return { ...this.designOverrides };
  }
}

// ============================================================================
// SECTION-SPECIFIC BUILDERS
// ============================================================================

/**
 * Base class for section builders with common functionality
 */
abstract class BaseSectionBuilder {
  constructor(protected builder: ContractDesignBuilder) {}

  /**
   * Return to main builder for chaining other sections
   */
  done(): ContractDesignBuilder {
    return this.builder;
  }
}

/**
 * Header section builder for company branding and title customization
 */
export class HeaderSectionBuilder extends BaseSectionBuilder {
  companyName(styles: any): this {
    this.builder._addSectionOverride('header', 'companyName', styles);
    return this;
  }

  organizationInfo(styles: any): this {
    this.builder._addSectionOverride('header', 'organizationInfo', styles);
    return this;
  }

  contractTitle(styles: any): this {
    this.builder._addSectionOverride('header', 'contractTitle', styles);
    return this;
  }

  container(styles: any): this {
    this.builder._addSectionOverride('header', 'container', styles);
    return this;
  }

  logo(styles: any): this {
    this.builder._addSectionOverride('header', 'logo', styles);
    return this;
  }
}

/**
 * Party identity section builder for employer/employee information
 */
export class PartyIdentitySectionBuilder extends BaseSectionBuilder {
  sectionTitle(styles: any): this {
    this.builder._addSectionOverride('partyIdentity', 'sectionTitle', styles);
    return this;
  }

  fieldLabel(styles: any): this {
    this.builder._addSectionOverride('partyIdentity', 'fieldLabel', styles);
    return this;
  }

  fieldValue(styles: any): this {
    this.builder._addSectionOverride('partyIdentity', 'fieldValue', styles);
    return this;
  }

  twoColumnRow(styles: any): this {
    this.builder._addSectionOverride('partyIdentity', 'twoColumnRow', styles);
    return this;
  }

  container(styles: any): this {
    this.builder._addSectionOverride('partyIdentity', 'container', styles);
    return this;
  }
}

/**
 * Work location section builder
 */
export class WorkLocationSectionBuilder extends BaseSectionBuilder {
  sectionTitle(styles: any): this {
    this.builder._addSectionOverride('workLocation', 'sectionTitle', styles);
    return this;
  }

  fieldLabel(styles: any): this {
    this.builder._addSectionOverride('workLocation', 'fieldLabel', styles);
    return this;
  }

  fieldValue(styles: any): this {
    this.builder._addSectionOverride('workLocation', 'fieldValue', styles);
    return this;
  }

  container(styles: any): this {
    this.builder._addSectionOverride('workLocation', 'container', styles);
    return this;
  }
}

/**
 * Employment terms section builder
 */
export class EmploymentTermsSectionBuilder extends BaseSectionBuilder {
  sectionTitle(styles: any): this {
    this.builder._addSectionOverride('employmentTerms', 'sectionTitle', styles);
    return this;
  }

  inlineLabel(styles: any): this {
    this.builder._addSectionOverride('employmentTerms', 'inlineLabel', styles);
    return this;
  }

  inlineValue(styles: any): this {
    this.builder._addSectionOverride('employmentTerms', 'inlineValue', styles);
    return this;
  }

  container(styles: any): this {
    this.builder._addSectionOverride('employmentTerms', 'container', styles);
    return this;
  }
}

/**
 * Work time & salary section builder
 */
export class WorkTimeSalarySectionBuilder extends BaseSectionBuilder {
  sectionTitle(styles: any): this {
    this.builder._addSectionOverride('workTimeSalary', 'sectionTitle', styles);
    return this;
  }

  inlineLabel(styles: any): this {
    this.builder._addSectionOverride('workTimeSalary', 'inlineLabel', styles);
    return this;
  }

  inlineValue(styles: any): this {
    this.builder._addSectionOverride('workTimeSalary', 'inlineValue', styles);
    return this;
  }

  container(styles: any): this {
    this.builder._addSectionOverride('workTimeSalary', 'container', styles);
    return this;
  }
}

/**
 * Standard sections builder (vacation, termination, pension, etc.)
 */
export class StandardSectionBuilder extends BaseSectionBuilder {
  sectionTitle(styles: any): this {
    this.builder._addSectionOverride('standardSections', 'sectionTitle', styles);
    return this;
  }

  inlineLabel(styles: any): this {
    this.builder._addSectionOverride('standardSections', 'inlineLabel', styles);
    return this;
  }

  inlineValue(styles: any): this {
    this.builder._addSectionOverride('standardSections', 'inlineValue', styles);
    return this;
  }

  container(styles: any): this {
    this.builder._addSectionOverride('standardSections', 'container', styles);
    return this;
  }

  keepTogether(styles: any): this {
    this.builder._addSectionOverride('standardSections', 'keepTogether', styles);
    return this;
  }
}

/**
 * Legal reference section builder
 */
export class LegalReferenceSectionBuilder extends BaseSectionBuilder {
  text(styles: any): this {
    this.builder._addSectionOverride('legalReference', 'text', styles);
    return this;
  }
}

/**
 * Signature section builder
 */
export class SignatureSectionBuilder extends BaseSectionBuilder {
  container(styles: any): this {
    this.builder._addSectionOverride('signature', 'container', styles);
    return this;
  }

  signatureBox(styles: any): this {
    this.builder._addSectionOverride('signature', 'signatureBox', styles);
    return this;
  }

  signatureLine(styles: any): this {
    this.builder._addSectionOverride('signature', 'signatureLine', styles);
    return this;
  }

  dateText(styles: any): this {
    this.builder._addSectionOverride('signature', 'dateText', styles);
    return this;
  }

  roleLabel(styles: any): this {
    this.builder._addSectionOverride('signature', 'roleLabel', styles);
    return this;
  }

  nameText(styles: any): this {
    this.builder._addSectionOverride('signature', 'nameText', styles);
    return this;
  }
}

/**
 * Footer section builder
 */
export class FooterSectionBuilder extends BaseSectionBuilder {
  pageNumber(styles: any): this {
    this.builder._addSectionOverride('footer', 'pageNumber', styles);
    return this;
  }
}

/**
 * Page layout section builder
 */
export class PageSectionBuilder extends BaseSectionBuilder {
  document(styles: any): this {
    this.builder._addSectionOverride('page', 'document', styles);
    return this;
  }
}

// ============================================================================
// DESIGN PRESETS
// ============================================================================

/**
 * Get predefined design presets for quick styling
 */
export function getDesignPresets() {
  return {
    /**
     * Professional preset - Clean, traditional business document styling
     */
    professional: {
      header: {
        companyName: { fontSize: 16, fontWeight: 'bold' as const },
        contractTitle: { fontSize: 18, letterSpacing: 1 }
      },
      partyIdentity: {
        sectionTitle: { fontSize: 12, fontWeight: 'bold' as const }
      },
      signature: {
        signatureLine: { borderBottomColor: '#000000' }
      }
    },

    /**
     * Modern preset - Contemporary styling with subtle color accents
     */
    modern: {
      header: {
        companyName: { fontSize: 18, color: '#3b82f6' },
        contractTitle: { fontSize: 20, color: '#1f2937' },
        container: { borderBottomColor: '#3b82f6' }
      },
      partyIdentity: {
        sectionTitle: { color: '#3b82f6' }
      },
      signature: {
        signatureLine: { borderBottomColor: '#3b82f6' }
      }
    },

    /**
     * Minimal preset - Clean, minimalist design with reduced visual weight
     */
    minimal: {
      header: {
        companyName: { fontSize: 14 },
        contractTitle: { fontSize: 16 },
        container: { borderBottomWidth: 0.5 }
      },
      partyIdentity: {
        sectionTitle: { fontSize: 11 }
      },
      signature: {
        signatureLine: { borderBottomWidth: 0.5 }
      }
    }
  };
}

// ============================================================================
// CONVENIENCE FUNCTIONS
// ============================================================================

/**
 * Quick function to create a design builder with a preset applied
 */
export function createDesignBuilder(preset?: 'professional' | 'modern' | 'minimal'): ContractDesignBuilder {
  const builder = new ContractDesignBuilder();
  if (preset) {
    builder.preset(preset);
  }
  return builder;
}

/**
 * Quick function to create a custom design with specific section overrides
 */
export function quickCustomDesign(overrides: Partial<Record<ContractSection, any>>): UnifiedContractDesign {
  return new ContractDesignBuilder().custom(overrides).build();
}

/**
 * Quick function to create a StyleSheet with specific customizations
 */
export function quickStyleSheet(overrides: Partial<Record<ContractSection, any>> = {}) {
  return new ContractDesignBuilder().custom(overrides).buildStyleSheet();
}
