/**
 * Unified Contract Design Template System
 * 
 * This module provides a centralized, section-by-section design configuration
 * for the arbeidskontrakt PDF generator. It eliminates configuration drift and
 * enables effortless incremental customization without cross-file navigation.
 * 
 * Key Features:
 * - Complete section isolation for independent styling
 * - Unified template with all customizable aspects consolidated
 * - Incremental design capabilities with strict separation
 * - Zero codebase bloat through intelligent organization
 * - Seamless mapping to React-PDF renderer
 */

import { StyleSheet } from '@react-pdf/renderer';
import { foundationTokens } from './contract-design-system';

// ============================================================================
// DESIGN FOUNDATION TOKENS
// ============================================================================

// Use existing foundation tokens to maintain consistency
export const designFoundation = foundationTokens;

// ============================================================================
// SECTION-BY-SECTION DESIGN TEMPLATES
// ============================================================================

/**
 * SECTION 1: Document Header
 * Company branding, title, and document identification
 */
export const headerSectionTemplate = {
  container: {
    textAlign: 'center' as const,
    marginBottom: designFoundation.spacing.section,
    paddingBottom: designFoundation.spacing.large,
    borderBottomWidth: designFoundation.layout.borders.thick,
    borderBottomColor: designFoundation.brand.primary
  },

  companyName: {
    fontSize: 14,
    fontWeight: designFoundation.typography.weights.bold,
    color: designFoundation.brand.primary,
    marginBottom: designFoundation.spacing.small
  },

  organizationInfo: {
    fontSize: 9,
    color: designFoundation.colors.text.secondary,
    marginBottom: designFoundation.spacing.tiny
  },

  contractTitle: {
    fontSize: 18,
    fontWeight: designFoundation.typography.weights.bold,
    color: designFoundation.brand.primary,
    marginTop: designFoundation.spacing.large,
    letterSpacing: 1
  },

  logoContainer: {
    marginBottom: designFoundation.spacing.medium,
    textAlign: 'center' as const
  },

  logo: {
    width: 80,
    height: 'auto'
  }
} as const;

/**
 * SECTION 2: Party Identity (Avtaleparter)
 * Employer and employee information in two-column layout
 */
export const partyIdentitySectionTemplate = {
  container: {
    marginBottom: designFoundation.spacing.section
  },

  sectionTitle: {
    fontSize: 11,
    fontWeight: designFoundation.typography.weights.bold,
    color: designFoundation.brand.primary,
    marginBottom: designFoundation.spacing.large
  },

  twoColumnRow: {
    flexDirection: 'row' as const,
    justifyContent: 'space-between' as const,
    gap: designFoundation.layout.columns.gap
  },

  employerColumn: {
    width: designFoundation.layout.columns.width
  },

  employeeColumn: {
    width: designFoundation.layout.columns.width
  },

  fieldLabel: {
    fontSize: 9,
    fontWeight: designFoundation.typography.weights.bold,
    color: designFoundation.colors.text.secondary,
    marginBottom: designFoundation.spacing.tiny
  },

  fieldValue: {
    fontSize: 10,
    color: designFoundation.brand.primary,
    marginBottom: designFoundation.spacing.medium,
    lineHeight: designFoundation.typography.lineHeights.normal
  }
} as const;

/**
 * SECTION 3: Work Location & Tasks (Arbeidssted og arbeidsoppgaver)
 * Job location, position, and task descriptions
 */
export const workLocationSectionTemplate = {
  container: {
    marginBottom: designFoundation.spacing.section
  },
  
  sectionTitle: {
    fontSize: 11,
    fontWeight: designFoundation.typography.weights.bold,
    color: designFoundation.brand.primary,
    marginBottom: designFoundation.spacing.large
  },
  
  fieldLabel: {
    fontSize: 9,
    fontWeight: designFoundation.typography.weights.bold,
    color: designFoundation.colors.text.secondary,
    marginBottom: designFoundation.spacing.tiny
  },

  fieldValue: {
    fontSize: 10,
    color: designFoundation.brand.primary,
    marginBottom: designFoundation.spacing.medium,
    lineHeight: designFoundation.typography.lineHeights.normal
  }
} as const;

/**
 * SECTION 4: Employment Terms (Ansettelsesforhold)
 * Start date, employment type, probation period
 */
export const employmentTermsSectionTemplate = {
  container: {
    marginBottom: designFoundation.spacing.section
  },

  sectionTitle: {
    fontSize: 11,
    fontWeight: designFoundation.typography.weights.bold,
    color: designFoundation.brand.primary,
    marginBottom: designFoundation.spacing.large
  },

  inlineLabel: {
    fontSize: 10,
    fontWeight: designFoundation.typography.weights.bold,
    color: designFoundation.colors.text.secondary
  },

  inlineValue: {
    fontSize: 10,
    color: designFoundation.brand.primary,
    marginBottom: designFoundation.spacing.small,
    lineHeight: designFoundation.typography.lineHeights.normal
  }
} as const;

/**
 * SECTION 5: Work Time & Salary (Arbeidstid og lønn)
 * Working hours, salary, overtime, payment details
 */
export const workTimeSalarySectionTemplate = {
  container: {
    marginBottom: designFoundation.spacing.section
  },
  
  sectionTitle: {
    fontSize: designFoundation.typography.sizes.md,
    fontWeight: designFoundation.typography.weights.bold,
    color: designFoundation.brand.primary,
    marginBottom: designFoundation.spacing.lg
  },
  
  inlineLabel: {
    fontSize: designFoundation.typography.sizes.base,
    fontWeight: designFoundation.typography.weights.bold,
    color: designFoundation.brand.secondary
  },
  
  inlineValue: {
    fontSize: designFoundation.typography.sizes.base,
    color: designFoundation.brand.primary,
    marginBottom: designFoundation.spacing.sm,
    lineHeight: designFoundation.typography.lineHeights.normal
  }
} as const;

/**
 * SECTIONS 6-9: Standard Contract Sections
 * Vacation, termination, pension, other terms - unified styling
 */
export const standardSectionTemplate = {
  container: {
    marginBottom: designFoundation.spacing.section
  },
  
  sectionTitle: {
    fontSize: designFoundation.typography.sizes.md,
    fontWeight: designFoundation.typography.weights.bold,
    color: designFoundation.brand.primary,
    marginBottom: designFoundation.spacing.lg
  },
  
  inlineLabel: {
    fontSize: designFoundation.typography.sizes.base,
    fontWeight: designFoundation.typography.weights.bold,
    color: designFoundation.brand.secondary
  },
  
  inlineValue: {
    fontSize: designFoundation.typography.sizes.base,
    color: designFoundation.brand.primary,
    marginBottom: designFoundation.spacing.sm,
    lineHeight: designFoundation.typography.lineHeights.normal
  },
  
  keepTogether: {
    breakInside: 'avoid' as const
  }
} as const;

/**
 * SECTION 10: Legal Reference
 * Legal compliance statement
 */
export const legalReferenceSectionTemplate = {
  text: {
    fontSize: designFoundation.typography.sizes.sm,
    color: designFoundation.brand.muted,
    fontStyle: 'italic' as const,
    marginTop: designFoundation.spacing.lg,
    marginBottom: designFoundation.spacing.xl,
    textAlign: 'center' as const,
    lineHeight: designFoundation.typography.lineHeights.relaxed
  }
} as const;

/**
 * SECTION 11: Contract Signature Area
 * Signature boxes for employer and employee
 */
export const signatureSectionTemplate = {
  container: {
    flexDirection: 'row' as const,
    justifyContent: 'space-between' as const,
    marginTop: designFoundation.spacing.xl,
    gap: designFoundation.layout.columnGap,
    breakInside: 'avoid' as const
  },
  
  signatureBox: {
    width: '45%',
    textAlign: 'center' as const
  },
  
  signatureLine: {
    borderBottomWidth: designFoundation.layout.borderWidth.medium,
    borderBottomColor: designFoundation.brand.primary,
    marginBottom: designFoundation.spacing.md,
    height: 40
  },
  
  dateText: {
    fontSize: designFoundation.typography.sizes.sm,
    color: designFoundation.brand.secondary,
    marginBottom: designFoundation.spacing.xs
  },
  
  roleLabel: {
    fontSize: designFoundation.typography.sizes.sm,
    fontWeight: designFoundation.typography.weights.bold,
    color: designFoundation.brand.secondary,
    marginBottom: designFoundation.spacing.xs
  },
  
  nameText: {
    fontSize: designFoundation.typography.sizes.base,
    color: designFoundation.brand.primary
  }
} as const;

/**
 * SECTION 12: Page Footer
 * Page numbering and document metadata
 */
export const pageFooterSectionTemplate = {
  pageNumber: {
    position: 'absolute' as const,
    bottom: 15,
    right: 30,
    fontSize: designFoundation.typography.sizes.xs,
    color: designFoundation.brand.muted
  }
} as const;

/**
 * SECTION 13: Page Layout
 * Overall document structure and page settings
 */
export const pageLayoutTemplate = {
  document: {
    backgroundColor: '#ffffff',
    color: designFoundation.brand.primary,
    fontSize: designFoundation.typography.sizes.base,
    lineHeight: designFoundation.typography.lineHeights.normal,
    fontFamily: designFoundation.typography.fontFamily,
    paddingTop: designFoundation.layout.pageMargin,
    paddingBottom: designFoundation.layout.pageMargin,
    paddingLeft: designFoundation.layout.pageMargin,
    paddingRight: designFoundation.layout.pageMargin
  }
} as const;

// ============================================================================
// UNIFIED CONTRACT DESIGN SYSTEM
// ============================================================================

/**
 * Complete unified design system for systematic section-by-section customization
 *
 * This is the single source of truth for all PDF design configuration.
 * Each section can be independently customized without affecting others.
 *
 * Usage Examples:
 *
 * 1. Customize header section:
 *    unifiedContractDesign.header.companyName.fontSize = 20
 *
 * 2. Modify signature area:
 *    unifiedContractDesign.signature.signatureLine.borderBottomColor = '#3b82f6'
 *
 * 3. Adjust spacing for all standard sections:
 *    unifiedContractDesign.standardSections.container.marginBottom = 30
 */
export const unifiedContractDesign = {
  // Document foundation
  page: pageLayoutTemplate,

  // Section 1: Document header and branding
  header: headerSectionTemplate,

  // Section 2: Party identification (employer/employee)
  partyIdentity: partyIdentitySectionTemplate,

  // Section 3: Work location and job tasks
  workLocation: workLocationSectionTemplate,

  // Section 4: Employment terms and conditions
  employmentTerms: employmentTermsSectionTemplate,

  // Section 5: Work time and salary details
  workTimeSalary: workTimeSalarySectionTemplate,

  // Sections 6-9: Standard contract sections (vacation, termination, etc.)
  standardSections: standardSectionTemplate,

  // Section 10: Legal compliance reference
  legalReference: legalReferenceSectionTemplate,

  // Section 11: Signature area
  signature: signatureSectionTemplate,

  // Section 12: Page footer and metadata
  footer: pageFooterSectionTemplate,

  // Design foundation tokens for advanced customization
  foundation: designFoundation
} as const;

// ============================================================================
// DESIGN CUSTOMIZATION INTERFACE
// ============================================================================

/**
 * Type-safe interface for customizing contract design
 */
export type UnifiedContractDesign = typeof unifiedContractDesign;

/**
 * Section identifiers for systematic customization
 */
export type ContractSection = keyof Omit<UnifiedContractDesign, 'foundation'>;

/**
 * Create a customized design configuration by overriding specific sections
 *
 * @param overrides - Partial design overrides organized by section
 * @returns Complete design configuration with overrides applied
 *
 * @example
 * ```typescript
 * const customDesign = createCustomDesign({
 *   header: {
 *     companyName: { fontSize: 20, color: '#3b82f6' },
 *     contractTitle: { fontSize: 22 }
 *   },
 *   signature: {
 *     signatureLine: { borderBottomColor: '#3b82f6' }
 *   }
 * });
 * ```
 */
export function createCustomDesign(
  overrides: Partial<Record<ContractSection, any>> = {}
): UnifiedContractDesign {
  const customDesign = { ...unifiedContractDesign };

  // Apply section-by-section overrides
  for (const [sectionKey, sectionOverrides] of Object.entries(overrides)) {
    const section = sectionKey as ContractSection;
    if (customDesign[section] && sectionOverrides) {
      customDesign[section] = {
        ...customDesign[section],
        ...deepMerge(customDesign[section], sectionOverrides)
      };
    }
  }

  return customDesign;
}

/**
 * Deep merge utility for nested design overrides
 */
function deepMerge(target: any, source: any): any {
  const result = { ...target };

  for (const key in source) {
    if (source[key] && typeof source[key] === 'object' && !Array.isArray(source[key])) {
      result[key] = deepMerge(result[key] || {}, source[key]);
    } else {
      result[key] = source[key];
    }
  }

  return result;
}

/**
 * Convert unified design to React-PDF StyleSheet
 *
 * @param design - Unified contract design configuration
 * @returns React-PDF StyleSheet ready for use in components
 */
export function createStyleSheet(design: UnifiedContractDesign = unifiedContractDesign) {
  return StyleSheet.create({
    // Page layout
    page: design.page.document,

    // Header section
    header: design.header.container,
    logoContainer: design.header.logoContainer,
    logo: design.header.logo,
    companyName: design.header.companyName,
    companyInfo: design.header.organizationInfo,
    title: design.header.contractTitle,

    // Party identity section
    partySection: design.partyIdentity.container,
    partySectionTitle: design.partyIdentity.sectionTitle,
    partyRow: design.partyIdentity.twoColumnRow,
    employerColumn: design.partyIdentity.employerColumn,
    employeeColumn: design.partyIdentity.employeeColumn,
    partyLabel: design.partyIdentity.fieldLabel,
    partyText: design.partyIdentity.fieldValue,

    // Work location section
    workSection: design.workLocation.container,
    workSectionTitle: design.workLocation.sectionTitle,
    workLabel: design.workLocation.fieldLabel,
    workText: design.workLocation.fieldValue,

    // Employment terms section
    employmentSection: design.employmentTerms.container,
    employmentSectionTitle: design.employmentTerms.sectionTitle,
    employmentLabel: design.employmentTerms.inlineLabel,
    employmentText: design.employmentTerms.inlineValue,

    // Work time & salary section
    salarySection: design.workTimeSalary.container,
    salarySectionTitle: design.workTimeSalary.sectionTitle,
    salaryLabel: design.workTimeSalary.inlineLabel,
    salaryText: design.workTimeSalary.inlineValue,

    // Standard sections (vacation, termination, etc.)
    standardSection: design.standardSections.container,
    standardSectionTitle: design.standardSections.sectionTitle,
    standardLabel: design.standardSections.inlineLabel,
    standardText: design.standardSections.inlineValue,
    keepTogether: design.standardSections.keepTogether,

    // Legal reference
    legalText: design.legalReference.text,

    // Signature section
    signatureSection: design.signature.container,
    signatureBox: design.signature.signatureBox,
    signatureLine: design.signature.signatureLine,
    signatureLabel: design.signature.roleLabel,
    signatureText: design.signature.nameText,
    signatureDate: design.signature.dateText,

    // Page footer
    pageNumber: design.footer.pageNumber
  });
}
