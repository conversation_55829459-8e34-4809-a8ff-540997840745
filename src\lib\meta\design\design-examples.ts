/**
 * Design Customization Examples for Arbeidskontrakt PDF Generator
 * 
 * This file demonstrates how to use the unified design system for systematic,
 * section-by-section customization of PDF contracts. Each example shows
 * different approaches to achieve specific design goals without code bloat.
 * 
 * Key Benefits Demonstrated:
 * - Section isolation - customize one section without affecting others
 * - Incremental design - make small, targeted changes
 * - No cross-file navigation - all customization in one place
 * - Maintainable configuration - clear, organized structure
 */

import { ContractDesignBuilder, createDesignBuilder, quickCustomDesign } from './section-design-builder';
import { unifiedContractDesign } from './unified-contract-template';

// ============================================================================
// EXAMPLE 1: BASIC SECTION CUSTOMIZATION
// ============================================================================

/**
 * Example: Customize header section only
 * 
 * This shows how to modify just the header without touching other sections.
 * Perfect for branding customization or company-specific styling.
 */
export const customHeaderExample = () => {
  return new ContractDesignBuilder()
    .header()
      .companyName({ 
        fontSize: 20, 
        color: '#3b82f6',
        fontWeight: 'bold' 
      })
      .contractTitle({ 
        fontSize: 22, 
        letterSpacing: 2,
        marginTop: 20 
      })
      .container({ 
        borderBottomColor: '#3b82f6',
        borderBottomWidth: 2 
      })
    .done()
    .build();
};

/**
 * Example: Customize signature area only
 * 
 * This demonstrates isolated signature section styling,
 * useful for different signature requirements or visual preferences.
 */
export const customSignatureExample = () => {
  return new ContractDesignBuilder()
    .signature()
      .signatureLine({ 
        borderBottomColor: '#059669',
        borderBottomWidth: 1.5 
      })
      .roleLabel({ 
        fontSize: 11,
        fontWeight: 'bold',
        color: '#059669' 
      })
      .container({ 
        marginTop: 30,
        gap: 30 
      })
    .done()
    .build();
};

// ============================================================================
// EXAMPLE 2: MULTI-SECTION CUSTOMIZATION
// ============================================================================

/**
 * Example: Professional blue theme across multiple sections
 * 
 * This shows how to apply consistent styling across different sections
 * while maintaining section isolation and clear organization.
 */
export const professionalBlueTheme = () => {
  return new ContractDesignBuilder()
    .header()
      .companyName({ color: '#1e40af', fontSize: 18 })
      .contractTitle({ color: '#1e40af', fontSize: 20 })
      .container({ borderBottomColor: '#1e40af' })
    .done()
    .partyIdentity()
      .sectionTitle({ color: '#1e40af', fontSize: 13 })
      .fieldLabel({ color: '#3730a3' })
    .done()
    .workLocation()
      .sectionTitle({ color: '#1e40af', fontSize: 13 })
      .fieldLabel({ color: '#3730a3' })
    .done()
    .employmentTerms()
      .sectionTitle({ color: '#1e40af', fontSize: 13 })
      .inlineLabel({ color: '#3730a3' })
    .done()
    .workTimeSalary()
      .sectionTitle({ color: '#1e40af', fontSize: 13 })
      .inlineLabel({ color: '#3730a3' })
    .done()
    .standardSections()
      .sectionTitle({ color: '#1e40af', fontSize: 13 })
      .inlineLabel({ color: '#3730a3' })
    .done()
    .signature()
      .signatureLine({ borderBottomColor: '#1e40af' })
      .roleLabel({ color: '#3730a3' })
    .done()
    .build();
};

/**
 * Example: Compact layout for shorter documents
 * 
 * This demonstrates how to reduce spacing and font sizes
 * across sections to create a more compact document layout.
 */
export const compactLayoutExample = () => {
  return new ContractDesignBuilder()
    .header()
      .companyName({ fontSize: 14 })
      .contractTitle({ fontSize: 16 })
      .container({ marginBottom: 16, paddingBottom: 8 })
    .done()
    .partyIdentity()
      .container({ marginBottom: 16 })
      .sectionTitle({ fontSize: 11 })
      .fieldLabel({ fontSize: 9 })
      .fieldValue({ fontSize: 10, marginBottom: 8 })
    .done()
    .workLocation()
      .container({ marginBottom: 16 })
      .sectionTitle({ fontSize: 11 })
      .fieldLabel({ fontSize: 9 })
      .fieldValue({ fontSize: 10, marginBottom: 8 })
    .done()
    .employmentTerms()
      .container({ marginBottom: 16 })
      .sectionTitle({ fontSize: 11 })
      .inlineValue({ fontSize: 10, marginBottom: 6 })
    .done()
    .workTimeSalary()
      .container({ marginBottom: 16 })
      .sectionTitle({ fontSize: 11 })
      .inlineValue({ fontSize: 10, marginBottom: 6 })
    .done()
    .standardSections()
      .container({ marginBottom: 16 })
      .sectionTitle({ fontSize: 11 })
      .inlineValue({ fontSize: 10, marginBottom: 6 })
    .done()
    .signature()
      .container({ marginTop: 20 })
    .done()
    .build();
};

// ============================================================================
// EXAMPLE 3: PRESET-BASED CUSTOMIZATION
// ============================================================================

/**
 * Example: Start with modern preset and add custom touches
 * 
 * This shows how to use presets as a foundation and then
 * add specific customizations on top.
 */
export const modernWithCustomTouches = () => {
  return createDesignBuilder('modern')
    .header()
      .companyName({ fontSize: 22 }) // Override preset
    .done()
    .signature()
      .signatureLine({ borderBottomWidth: 2 }) // Add custom styling
    .done()
    .build();
};

/**
 * Example: Professional preset with company branding
 * 
 * This demonstrates how to take a professional preset
 * and customize it for specific company branding needs.
 */
export const professionalWithBranding = () => {
  return createDesignBuilder('professional')
    .header()
      .companyName({ 
        color: '#dc2626', // Company red
        fontSize: 19 
      })
      .contractTitle({ 
        color: '#dc2626',
        fontSize: 21 
      })
      .container({ 
        borderBottomColor: '#dc2626' 
      })
    .done()
    .signature()
      .signatureLine({ 
        borderBottomColor: '#dc2626' 
      })
    .done()
    .build();
};

// ============================================================================
// EXAMPLE 4: QUICK CUSTOMIZATION METHODS
// ============================================================================

/**
 * Example: Quick custom design using direct overrides
 * 
 * This shows the quickest way to apply specific customizations
 * when you know exactly what you want to change.
 */
export const quickHeaderCustomization = () => {
  return quickCustomDesign({
    header: {
      companyName: { fontSize: 24, color: '#059669' },
      contractTitle: { fontSize: 26, color: '#059669' }
    },
    signature: {
      signatureLine: { borderBottomColor: '#059669' }
    }
  });
};

/**
 * Example: Minimal spacing adjustments
 * 
 * This demonstrates how to make subtle spacing adjustments
 * across multiple sections for better visual balance.
 */
export const spacingAdjustments = () => {
  return quickCustomDesign({
    partyIdentity: {
      container: { marginBottom: 28 }
    },
    workLocation: {
      container: { marginBottom: 28 }
    },
    employmentTerms: {
      container: { marginBottom: 28 }
    },
    workTimeSalary: {
      container: { marginBottom: 28 }
    },
    standardSections: {
      container: { marginBottom: 28 }
    }
  });
};

// ============================================================================
// EXAMPLE 5: CONDITIONAL CUSTOMIZATION
// ============================================================================

/**
 * Example: Dynamic customization based on contract type
 * 
 * This shows how to apply different styling based on
 * contract parameters or business logic.
 */
export const createContractDesign = (contractType: 'permanent' | 'temporary' | 'consultant') => {
  const builder = new ContractDesignBuilder();

  // Base styling for all contracts
  builder
    .header()
      .companyName({ fontSize: 18, fontWeight: 'bold' })
      .contractTitle({ fontSize: 20 })
    .done();

  // Conditional styling based on contract type
  switch (contractType) {
    case 'permanent':
      builder
        .header()
          .contractTitle({ color: '#059669' }) // Green for permanent
        .done()
        .signature()
          .signatureLine({ borderBottomColor: '#059669' })
        .done();
      break;

    case 'temporary':
      builder
        .header()
          .contractTitle({ color: '#dc2626' }) // Red for temporary
        .done()
        .signature()
          .signatureLine({ borderBottomColor: '#dc2626' })
        .done();
      break;

    case 'consultant':
      builder
        .header()
          .contractTitle({ color: '#7c3aed' }) // Purple for consultant
        .done()
        .signature()
          .signatureLine({ borderBottomColor: '#7c3aed' })
        .done();
      break;
  }

  return builder.build();
};

// ============================================================================
// EXAMPLE 6: ACCESSIBILITY AND READABILITY
// ============================================================================

/**
 * Example: High contrast design for better readability
 * 
 * This demonstrates how to create a high-contrast version
 * for better accessibility and readability.
 */
export const highContrastDesign = () => {
  return new ContractDesignBuilder()
    .header()
      .companyName({ 
        fontSize: 20, 
        fontWeight: 'bold',
        color: '#000000' 
      })
      .contractTitle({ 
        fontSize: 22, 
        fontWeight: 'bold',
        color: '#000000' 
      })
      .container({ 
        borderBottomColor: '#000000',
        borderBottomWidth: 2 
      })
    .done()
    .partyIdentity()
      .sectionTitle({ 
        fontSize: 14, 
        fontWeight: 'bold',
        color: '#000000' 
      })
      .fieldLabel({ 
        fontSize: 11, 
        fontWeight: 'bold',
        color: '#000000' 
      })
      .fieldValue({ 
        fontSize: 12,
        color: '#000000',
        lineHeight: 1.6 
      })
    .done()
    .signature()
      .signatureLine({ 
        borderBottomColor: '#000000',
        borderBottomWidth: 2 
      })
      .roleLabel({ 
        fontSize: 12,
        fontWeight: 'bold',
        color: '#000000' 
      })
    .done()
    .build();
};
