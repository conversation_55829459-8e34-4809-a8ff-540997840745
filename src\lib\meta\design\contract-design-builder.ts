/**
 * Contract Design Builder - Systematic Section-by-Section Design System
 * 
 * This module provides utilities for building and customizing contract designs
 * in a systematic, section-by-section manner without code bloat.
 * 
 * Key Features:
 * - Deep merge capabilities for design overrides
 * - Type-safe design building with validation
 * - Section isolation and independence
 * - Runtime design customization
 * - Consistent design token application
 */

import { StyleSheet } from '@react-pdf/renderer';
import { 
  ContractDesignConfig, 
  SectionDesignConfig, 
  SectionDesignProps,
  ContractSectionId,
  SectionElementType,
  designVariants,
  DesignVariantName,
  defaultDesignConfig
} from './contract-design-config';
import { foundationTokens } from './contract-design-system';

// ============================================================================
// DESIGN BUILDER UTILITIES
// ============================================================================

/**
 * Deep merge two design configurations, with override taking precedence
 */
export function mergeDesignConfigs(
  base: ContractDesignConfig,
  override: Partial<ContractDesignConfig>
): ContractDesignConfig {
  const result: ContractDesignConfig = { ...base };
  
  for (const [sectionId, sectionConfig] of Object.entries(override)) {
    if (sectionConfig) {
      const typedSectionId = sectionId as ContractSectionId;
      result[typedSectionId] = {
        ...result[typedSectionId],
        ...sectionConfig
      };
      
      // Deep merge section elements
      for (const [elementType, elementProps] of Object.entries(sectionConfig)) {
        if (elementProps) {
          const typedElementType = elementType as SectionElementType;
          result[typedSectionId] = result[typedSectionId] || {};
          result[typedSectionId]![typedElementType] = {
            ...result[typedSectionId]![typedElementType],
            ...elementProps
          };
        }
      }
    }
  }
  
  return result;
}

/**
 * Get design configuration for a specific section
 */
export function getSectionDesign(
  config: ContractDesignConfig,
  sectionId: ContractSectionId
): SectionDesignConfig {
  return config[sectionId] || {};
}

/**
 * Get design properties for a specific element within a section
 */
export function getElementDesign(
  config: ContractDesignConfig,
  sectionId: ContractSectionId,
  elementType: SectionElementType
): SectionDesignProps {
  return config[sectionId]?.[elementType] || {};
}

/**
 * Create a design configuration from a variant with optional overrides
 */
export function createDesignConfig(
  variant: DesignVariantName = 'professional',
  overrides: Partial<ContractDesignConfig> = {}
): ContractDesignConfig {
  const baseConfig = designVariants[variant];
  return mergeDesignConfigs(baseConfig, overrides);
}

/**
 * Validate design configuration for consistency and completeness
 */
export function validateDesignConfig(config: ContractDesignConfig): {
  isValid: boolean;
  errors: string[];
  warnings: string[];
} {
  const errors: string[] = [];
  const warnings: string[] = [];
  
  // Check for required sections
  const requiredSections: ContractSectionId[] = [
    'page', 'header', 'partyIdentity', 'signature'
  ];
  
  for (const sectionId of requiredSections) {
    if (!config[sectionId]) {
      warnings.push(`Missing configuration for required section: ${sectionId}`);
    }
  }
  
  // Validate color values
  for (const [sectionId, sectionConfig] of Object.entries(config)) {
    if (sectionConfig) {
      for (const [elementType, elementProps] of Object.entries(sectionConfig)) {
        if (elementProps) {
          // Check color format
          const colorProps = ['color', 'backgroundColor', 'borderTopColor', 'borderBottomColor'];
          for (const colorProp of colorProps) {
            const colorValue = (elementProps as any)[colorProp];
            if (colorValue && typeof colorValue === 'string') {
              if (!colorValue.match(/^#[0-9a-fA-F]{6}$/) && !colorValue.match(/^#[0-9a-fA-F]{3}$/)) {
                errors.push(`Invalid color format in ${sectionId}.${elementType}.${colorProp}: ${colorValue}`);
              }
            }
          }
          
          // Check numeric values
          const numericProps = ['fontSize', 'lineHeight', 'marginTop', 'marginBottom', 'paddingTop', 'paddingBottom'];
          for (const numericProp of numericProps) {
            const numericValue = (elementProps as any)[numericProp];
            if (numericValue !== undefined && (typeof numericValue !== 'number' || numericValue < 0)) {
              errors.push(`Invalid numeric value in ${sectionId}.${elementType}.${numericProp}: ${numericValue}`);
            }
          }
        }
      }
    }
  }
  
  return {
    isValid: errors.length === 0,
    errors,
    warnings
  };
}

// ============================================================================
// STYLE SHEET BUILDER
// ============================================================================

/**
 * Convert design configuration to React-PDF StyleSheet
 */
export function buildStyleSheet(config: ContractDesignConfig) {
  const styles: Record<string, any> = {};
  
  // Build styles for each section
  for (const [sectionId, sectionConfig] of Object.entries(config)) {
    if (sectionConfig) {
      for (const [elementType, elementProps] of Object.entries(sectionConfig)) {
        if (elementProps) {
          const styleKey = `${sectionId}_${elementType}`;
          styles[styleKey] = elementProps;
        }
      }
    }
  }
  
  return StyleSheet.create(styles);
}

/**
 * Get style key for a specific section element
 */
export function getStyleKey(sectionId: ContractSectionId, elementType: SectionElementType): string {
  return `${sectionId}_${elementType}`;
}

// ============================================================================
// DESIGN CUSTOMIZATION HELPERS
// ============================================================================

/**
 * Create a section-specific design override
 */
export function createSectionOverride(
  sectionId: ContractSectionId,
  overrides: SectionDesignConfig
): Partial<ContractDesignConfig> {
  return {
    [sectionId]: overrides
  };
}

/**
 * Create an element-specific design override
 */
export function createElementOverride(
  sectionId: ContractSectionId,
  elementType: SectionElementType,
  overrides: SectionDesignProps
): Partial<ContractDesignConfig> {
  return {
    [sectionId]: {
      [elementType]: overrides
    }
  };
}

/**
 * Apply foundation tokens to design properties
 */
export function applyFoundationTokens(props: SectionDesignProps): SectionDesignProps {
  const result = { ...props };
  
  // Apply font family if not specified
  if (!result.fontFamily) {
    (result as any).fontFamily = foundationTokens.typography.fontFamily;
  }
  
  // Apply default colors if not specified
  if (!result.color) {
    result.color = foundationTokens.colors.text.primary;
  }
  
  return result;
}

// ============================================================================
// PRESET CUSTOMIZATIONS
// ============================================================================

/**
 * Common design customizations for quick application
 */
export const designPresets = {
  // Header customizations
  largerHeader: createSectionOverride('header', {
    title: { fontSize: 20, letterSpacing: 1.5 },
    container: { marginBottom: 50 }
  }),
  
  coloredHeader: createSectionOverride('header', {
    title: { color: '#3b82f6' },
    container: { borderBottomColor: '#3b82f6', borderBottomWidth: 2 }
  }),
  
  // Signature customizations
  largerSignature: createSectionOverride('signature', {
    container: { marginTop: 60, paddingTop: 30 },
    line: { height: 60, marginBottom: 20 }
  }),
  
  // Spacing customizations
  compactSpacing: {
    partyIdentity: { container: { marginBottom: 15 } },
    workLocationTasks: { container: { marginBottom: 15 } },
    employmentTerms: { container: { marginBottom: 15 } },
    workTimeSalary: { container: { marginBottom: 15 } }
  } as Partial<ContractDesignConfig>,
  
  relaxedSpacing: {
    partyIdentity: { container: { marginBottom: 25 } },
    workLocationTasks: { container: { marginBottom: 25 } },
    employmentTerms: { container: { marginBottom: 25 } },
    workTimeSalary: { container: { marginBottom: 25 } }
  } as Partial<ContractDesignConfig>
};

// ============================================================================
// MAIN DESIGN BUILDER CLASS
// ============================================================================

/**
 * Main design builder for systematic contract customization
 */
export class ContractDesignBuilder {
  private config: ContractDesignConfig;
  
  constructor(variant: DesignVariantName = 'professional') {
    this.config = designVariants[variant];
  }
  
  /**
   * Apply design overrides
   */
  override(overrides: Partial<ContractDesignConfig>): this {
    this.config = mergeDesignConfigs(this.config, overrides);
    return this;
  }
  
  /**
   * Customize a specific section
   */
  section(sectionId: ContractSectionId, overrides: SectionDesignConfig): this {
    return this.override(createSectionOverride(sectionId, overrides));
  }
  
  /**
   * Customize a specific element
   */
  element(sectionId: ContractSectionId, elementType: SectionElementType, overrides: SectionDesignProps): this {
    return this.override(createElementOverride(sectionId, elementType, overrides));
  }
  
  /**
   * Apply a preset customization
   */
  preset(preset: Partial<ContractDesignConfig>): this {
    return this.override(preset);
  }
  
  /**
   * Build the final configuration
   */
  build(): ContractDesignConfig {
    return this.config;
  }
  
  /**
   * Build and validate the configuration
   */
  buildWithValidation(): { config: ContractDesignConfig; validation: ReturnType<typeof validateDesignConfig> } {
    const validation = validateDesignConfig(this.config);
    return { config: this.config, validation };
  }
  
  /**
   * Build React-PDF StyleSheet
   */
  buildStyleSheet() {
    return buildStyleSheet(this.config);
  }
}
