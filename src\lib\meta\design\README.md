# Unified Contract Design System

## 🎯 **Overview**

This unified design system provides **effortless, section-by-section customization** for the Arbeidskontrakt PDF generator. It eliminates configuration drift, prevents codebase bloat, and enables incremental customization without cross-file navigation.

## 📁 **File Structure**

```
src/lib/meta/design/
├── unified-contract-template.ts    # 🎯 MAIN: Unified design system
├── section-design-builder.ts       # 🛠️ Fluent API for customization
├── design-examples.ts              # 📚 Practical usage examples
├── contract-design-system.ts       # 📜 Legacy (maintained for compatibility)
├── contract-design-builder.ts      # 📜 Legacy builder utilities
├── contract-design-config.ts       # 📜 Legacy configuration
└── README.md                       # 📖 This documentation
```

## 🌟 **Key Features**

- **Complete Section Isolation**: Customize each contract section independently
- **Zero Configuration Drift**: Centralized design management prevents inconsistencies
- **Incremental Customization**: Make targeted changes without affecting other sections
- **No Cross-File Navigation**: All design configuration in one unified system
- **Type-Safe Design Building**: IntelliSense support and compile-time validation
- **Preset Support**: Professional, modern, and minimal design variants
- **Fluent API**: Chainable interface for intuitive customization

## 🚀 **Quick Start**

### **1. Basic Usage (Default Design)**

```typescript
import { ContractPDF } from '@/components/Meta/ContractPDFTemplate';

// Use default unified design
<ContractPDF formData={formData} />
```

### **2. Section-by-Section Customization**

```typescript
import { ContractDesignBuilder } from '@/lib/meta/design/section-design-builder';

// Customize specific sections with fluent API
const customDesign = new ContractDesignBuilder()
  .header()
    .companyName({ fontSize: 20, color: '#3b82f6' })
    .contractTitle({ fontSize: 22, letterSpacing: 2 })
  .done()
  .signature()
    .signatureLine({ borderBottomColor: '#3b82f6' })
  .done()
  .build();

// Apply to PDF
<ContractPDF formData={formData} customDesign={customDesign} />
```

### **3. Preset-Based Customization**

```typescript
import { createDesignBuilder } from '@/lib/meta/design/section-design-builder';

// Start with a preset and add custom touches
const design = createDesignBuilder('modern')
  .header()
    .companyName({ fontSize: 22 }) // Override preset
  .done()
  .build();
```

### **4. Quick Customization**

```typescript
import { quickCustomDesign } from '@/lib/meta/design/section-design-builder';

// Quick overrides for specific sections
const design = quickCustomDesign({
  header: {
    companyName: { fontSize: 24, color: '#059669' },
    contractTitle: { fontSize: 26 }
  },
  signature: {
    signatureLine: { borderBottomColor: '#059669' }
  }
});
```

## 🛠️ **How to Customize**

### **Quick Color Changes**
```typescript
// In contract-design-system.ts
colors: {
  primary: '#059669',    // Change to your brand color
  secondary: '#374151',  // Dark gray for titles
  accent: '#d1d5db',     // Light gray for borders
}
```

### **Typography Adjustments**
```typescript
typography: {
  sizes: {
    xs: 9,    // Legal text
    sm: 11,   // Body text
    md: 13,   // Section titles
    lg: 16,   // Main title
    xl: 18,   // Company name
  }
}
```

### **Spacing Modifications**
```typescript
spacing: {
  xs: 2,    // Tiny gaps
  sm: 5,    // Small gaps
  md: 10,   // Medium gaps
  lg: 15,   // Large gaps
  xl: 20,   // Extra large gaps
  xxl: 30,  // Page margins
  xxxl: 40, // Major sections
}
```

## 📋 **Section-by-Section Customization**

### **Header Section**
```typescript
headerDesign = {
  container: { /* Header container styling */ },
  companyName: { /* Company name styling */ },
  companyInfo: { /* Company info styling */ },
  title: { /* Contract title styling */ }
}
```

**What it controls:**
- Company name size, color, weight
- Company info (org number, address) styling
- Contract title appearance
- Header borders and spacing

### **Content Sections**
```typescript
contentDesign = {
  section: { /* Section container */ },
  sectionTitle: { /* Section headers */ },
  row: { /* Two-column layouts */ },
  column: { /* Column content */ },
  label: { /* Field labels */ },
  text: { /* Body text */ },
  keepTogether: { /* Page break controls */ }
}
```

**What it controls:**
- Section spacing and margins
- Section title colors and borders
- Two-column vs single-column layouts
- Label and text styling
- Page break behavior

### **Signature Section**
```typescript
signatureDesign = {
  container: { /* Signature area container */ },
  box: { /* Individual signature boxes */ },
  line: { /* Signature lines */ },
  label: { /* Signature labels */ },
  text: { /* Signature text */ }
}
```

**What it controls:**
- Signature area positioning
- Signature line appearance
- Date and name styling
- Box spacing and alignment

### **Footer Section**
```typescript
footerDesign = {
  pageNumber: { /* Page numbering styling and positioning */ }
}
```

**What it controls:**
- Page number positioning (lower right corner)
- Page number font size and color
- Absolute positioning from bottom and right edges

## 🚀 **Usage Examples**

### **Example 1: Change Brand Color**
```typescript
// In contract-design-system.ts
colors: {
  primary: '#1e40af',  // Change from green to blue
  // ... rest unchanged
}
```
**Result:** All section titles, company name, and borders become blue.

### **Example 2: Increase Section Spacing**
```typescript
// In contract-design-system.ts
spacing: {
  // ... existing values
  lg: 20,   // Increase from 15 to 20
  xl: 25,   // Increase from 20 to 25
}
```
**Result:** More breathing room between sections.

### **Example 3: Larger Company Name**
```typescript
// In contract-design-system.ts
headerDesign = {
  companyName: {
    fontSize: designTokens.typography.sizes.xl + 4, // 22px instead of 18px
    // ... rest unchanged
  }
}
```
**Result:** More prominent company branding.

## 🎯 **Benefits**

### **Before (Old System)**
- ❌ Styles scattered across 80+ lines
- ❌ Hard-coded values everywhere
- ❌ Cross-file hunting for changes
- ❌ Risk of breaking other sections
- ❌ Inconsistent spacing/colors

### **After (New System)**
- ✅ Single file controls everything
- ✅ Logical section-based organization
- ✅ Design tokens prevent inconsistency
- ✅ Change one section without affecting others
- ✅ Clear documentation and examples

## 🔧 **Advanced Customization**

### **Adding New Design Tokens**
```typescript
designTokens = {
  // Add new token categories
  shadows: {
    light: '0 1px 3px rgba(0,0,0,0.1)',
    medium: '0 4px 6px rgba(0,0,0,0.1)',
  },
  borders: {
    radius: 4,
    style: 'solid',
  }
}
```

### **Creating New Section Groups**
```typescript
// Add new section for future features
export const footerDesign = {
  container: { /* Footer styling */ },
  text: { /* Footer text */ },
} as const;
```

## 📝 **Best Practices**

1. **Always use design tokens** instead of hard-coded values
2. **Test changes incrementally** - modify one section at a time
3. **Keep section isolation** - don't mix styles between sections
4. **Document custom changes** - add comments for future reference
5. **Use TypeScript** - leverage type safety for consistency

## 🎉 **Result**

**Effortless, incremental section design** with **zero configuration drift** and **absolute maintainability** - all through one importable config! 🚀
