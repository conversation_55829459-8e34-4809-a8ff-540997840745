/**
 * Design System Exports
 * 
 * Centralized export file for the arbeidskontrakt design system.
 * This follows the established project pattern of using index.ts files
 * for clean, organized imports.
 */

// ============================================================================
// UNIFIED DESIGN SYSTEM (RECOMMENDED)
// ============================================================================

// Main unified design system
export {
  unifiedContractDesign,
  createCustomDesign,
  createStyleSheet,
  designFoundation,
  // Section templates
  headerSectionTemplate,
  partyIdentitySectionTemplate,
  workLocationSectionTemplate,
  employmentTermsSectionTemplate,
  workTimeSalarySectionTemplate,
  standardSectionTemplate,
  legalReferenceSectionTemplate,
  signatureSectionTemplate,
  pageFooterSectionTemplate,
  pageLayoutTemplate
} from './unified-contract-template';

// Design builder and utilities
export {
  ContractDesignBuilder,
  createDesignBuilder,
  quickCustomDesign,
  quickStyleSheet,
  getDesignPresets,
  // Section builders
  HeaderSectionBuilder,
  PartyIdentitySectionBuilder,
  WorkLocationSectionBuilder,
  EmploymentTermsSectionBuilder,
  WorkTimeSalarySectionBuilder,
  StandardSectionBuilder,
  LegalReferenceSectionBuilder,
  SignatureSectionBuilder,
  FooterSectionBuilder,
  PageSectionBuilder
} from './section-design-builder';

// Types
export type {
  UnifiedContractDesign,
  ContractSection
} from './unified-contract-template';

// ============================================================================
// LEGACY DESIGN SYSTEM (MAINTAINED FOR COMPATIBILITY)
// ============================================================================

// Legacy design system - maintained for backward compatibility
export {
  contractDesignSystem,
  foundationTokens,
  // Legacy section designs
  contractHeaderDesign,
  partyIdentityDesign,
  workLocationTasksDesign,
  employmentTermsDesign,
  workTimeSalaryDesign,
  standardSectionsDesign,
  legalReferenceDesign,
  contractSignatureDesign,
  pageFooterDesign,
  pageDesign
} from './contract-design-system';

// Legacy builder utilities
export {
  mergeDesignConfigs,
  getSectionDesign,
  getElementDesign,
  createDesignConfig,
  validateDesignConfig,
  buildStyleSheet,
  ContractDesignBuilder as LegacyContractDesignBuilder
} from './contract-design-builder';

// Legacy configuration types
export type {
  ContractDesignConfig,
  SectionDesignConfig,
  SectionDesignProps,
  ContractSectionId,
  SectionElementType,
  DesignVariantName
} from './contract-design-config';

// ============================================================================
// DESIGN EXAMPLES
// ============================================================================

// Design examples for reference
export {
  customHeaderExample,
  customSignatureExample,
  professionalBlueTheme,
  compactLayoutExample,
  modernWithCustomTouches,
  professionalWithBranding,
  quickHeaderCustomization,
  spacingAdjustments,
  createContractDesign,
  highContrastDesign
} from './design-examples';

// ============================================================================
// MIGRATION GUIDE
// ============================================================================

/**
 * Migration Guide for Existing Code
 * 
 * If you're currently using the legacy design system, here's how to migrate:
 * 
 * OLD (Legacy):
 * ```typescript
 * import { contractDesignSystem } from '@/lib/meta/design/contract-design-system';
 * const styles = StyleSheet.create({
 *   header: contractDesignSystem.contractHeader.container
 * });
 * ```
 * 
 * NEW (Unified):
 * ```typescript
 * import { createStyleSheet, unifiedContractDesign } from '@/lib/meta/design';
 * const styles = createStyleSheet(unifiedContractDesign);
 * ```
 * 
 * CUSTOMIZATION (New):
 * ```typescript
 * import { ContractDesignBuilder } from '@/lib/meta/design';
 * const customDesign = new ContractDesignBuilder()
 *   .header()
 *     .companyName({ fontSize: 20 })
 *   .done()
 *   .build();
 * const styles = createStyleSheet(customDesign);
 * ```
 */
