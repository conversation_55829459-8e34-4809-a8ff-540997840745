/**
 * Enhanced Section-Based Design Configuration for Arbeidskontrakt PDF Generator
 * 
 * This configuration system enables systematic, section-by-section PDF design customization
 * without code bloat. Each section can be independently styled while maintaining consistency.
 * 
 * Key Features:
 * - Granular section control (header, content blocks, signing area)
 * - Design variant support (professional, modern, minimal)
 * - Runtime customization capabilities
 * - Type-safe configuration with validation
 * - Zero configuration drift
 */

import { foundationTokens } from './contract-design-system';

// ============================================================================
// DESIGN CONFIGURATION INTERFACES
// ============================================================================

/**
 * Core design properties that can be customized for any section
 */
export interface SectionDesignProps {
  // Typography
  fontSize?: number;
  fontWeight?: 'normal' | 'bold';
  lineHeight?: number;
  letterSpacing?: number;
  textAlign?: 'left' | 'center' | 'right';
  
  // Colors
  color?: string;
  backgroundColor?: string;
  
  // Spacing
  marginTop?: number;
  marginBottom?: number;
  marginLeft?: number;
  marginRight?: number;
  paddingTop?: number;
  paddingBottom?: number;
  paddingLeft?: number;
  paddingRight?: number;
  
  // Borders
  borderTopWidth?: number;
  borderBottomWidth?: number;
  borderLeftWidth?: number;
  borderRightWidth?: number;
  borderTopColor?: string;
  borderBottomColor?: string;
  borderLeftColor?: string;
  borderRightColor?: string;
  
  // Layout
  flexDirection?: 'row' | 'column';
  justifyContent?: 'flex-start' | 'center' | 'flex-end' | 'space-between' | 'space-around';
  alignItems?: 'flex-start' | 'center' | 'flex-end' | 'stretch';
  width?: string | number;
  height?: string | number;
  
  // Page breaks
  breakInside?: 'auto' | 'avoid';
  breakAfter?: 'auto' | 'avoid';
  orphans?: number;
  widows?: number;
}

/**
 * Contract section identifiers for systematic organization
 */
export type ContractSectionId = 
  | 'page'
  | 'header'
  | 'partyIdentity'
  | 'workLocationTasks'
  | 'employmentTerms'
  | 'workTimeSalary'
  | 'vacationLeave'
  | 'noticeTermination'
  | 'pensionInsurance'
  | 'otherTerms'
  | 'legalReference'
  | 'signature'
  | 'footer';

/**
 * Element types within each section for granular control
 */
export type SectionElementType =
  | 'container'
  | 'title'
  | 'label'
  | 'value'
  | 'text'
  | 'line'
  | 'box'
  | 'column'
  | 'row';

/**
 * Complete section design configuration
 */
export interface SectionDesignConfig {
  [elementType in SectionElementType]?: SectionDesignProps;
}

/**
 * Full contract design configuration mapping
 */
export interface ContractDesignConfig {
  [sectionId in ContractSectionId]?: SectionDesignConfig;
}

// ============================================================================
// DESIGN VARIANTS
// ============================================================================

/**
 * Professional variant - Clean, traditional business document style
 */
export const professionalVariant: ContractDesignConfig = {
  page: {
    container: {
      backgroundColor: '#ffffff',
      color: '#000000',
      fontSize: 11,
      lineHeight: 1.4,
      paddingTop: 30,
      paddingBottom: 30,
      paddingLeft: 30,
      paddingRight: 30,
    }
  },
  
  header: {
    container: {
      textAlign: 'center',
      marginBottom: 40,
      paddingBottom: 16,
      borderBottomWidth: 1,
      borderBottomColor: '#000000',
    },
    title: {
      fontSize: 16,
      fontWeight: 'bold',
      letterSpacing: 1,
      marginTop: 16,
    },
    text: {
      fontSize: 11,
      color: '#374151',
      marginBottom: 2,
    }
  },
  
  partyIdentity: {
    container: {
      marginBottom: 20,
      breakInside: 'avoid',
    },
    title: {
      fontSize: 13,
      fontWeight: 'bold',
      marginBottom: 12,
      paddingBottom: 4,
      borderBottomWidth: 0.5,
      borderBottomColor: '#dbdbdb',
    },
    row: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      marginBottom: 16,
    },
    column: {
      width: '45%',
    },
    label: {
      fontWeight: 'bold',
      fontSize: 11,
      marginBottom: 2,
    },
    value: {
      fontSize: 11,
      marginBottom: 8,
      lineHeight: 1.4,
    }
  },
  
  signature: {
    container: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      marginTop: 40,
      paddingTop: 20,
      borderTopWidth: 0.5,
      borderTopColor: '#dbdbdb',
      breakInside: 'avoid',
    },
    box: {
      width: '45%',
      textAlign: 'center',
    },
    line: {
      borderTopWidth: 0.5,
      borderTopColor: '#9ca3af',
      height: 40,
      marginBottom: 12,
    },
    label: {
      fontWeight: 'bold',
      fontSize: 11,
      marginBottom: 2,
    },
    text: {
      fontSize: 11,
      marginBottom: 2,
    }
  }
};

/**
 * Modern variant - Clean, contemporary design with subtle styling
 */
export const modernVariant: ContractDesignConfig = {
  page: {
    container: {
      backgroundColor: '#ffffff',
      color: '#1f2937',
      fontSize: 11,
      lineHeight: 1.5,
      paddingTop: 35,
      paddingBottom: 35,
      paddingLeft: 35,
      paddingRight: 35,
    }
  },
  
  header: {
    container: {
      textAlign: 'center',
      marginBottom: 45,
      paddingBottom: 20,
      borderBottomWidth: 2,
      borderBottomColor: '#3b82f6',
    },
    title: {
      fontSize: 18,
      fontWeight: 'bold',
      letterSpacing: 1.2,
      marginTop: 18,
      color: '#1f2937',
    },
    text: {
      fontSize: 11,
      color: '#6b7280',
      marginBottom: 3,
    }
  },
  
  partyIdentity: {
    title: {
      fontSize: 14,
      fontWeight: 'bold',
      marginBottom: 14,
      paddingBottom: 6,
      borderBottomWidth: 1,
      borderBottomColor: '#e5e7eb',
      color: '#3b82f6',
    }
  },
  
  signature: {
    container: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      marginTop: 45,
      paddingTop: 25,
      borderTopWidth: 1,
      borderTopColor: '#e5e7eb',
    },
    line: {
      borderTopWidth: 1,
      borderTopColor: '#3b82f6',
      height: 45,
      marginBottom: 15,
    }
  }
};

/**
 * Minimal variant - Ultra-clean, minimal design with maximum readability
 */
export const minimalVariant: ContractDesignConfig = {
  page: {
    container: {
      backgroundColor: '#ffffff',
      color: '#000000',
      fontSize: 12,
      lineHeight: 1.6,
      paddingTop: 40,
      paddingBottom: 40,
      paddingLeft: 40,
      paddingRight: 40,
    }
  },
  
  header: {
    container: {
      textAlign: 'left',
      marginBottom: 50,
      paddingBottom: 0,
      borderBottomWidth: 0,
    },
    title: {
      fontSize: 20,
      fontWeight: 'bold',
      letterSpacing: 0,
      marginTop: 20,
      textAlign: 'left',
    }
  },
  
  partyIdentity: {
    title: {
      fontSize: 14,
      fontWeight: 'bold',
      marginBottom: 16,
      paddingBottom: 0,
      borderBottomWidth: 0,
    }
  },
  
  signature: {
    container: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      marginTop: 50,
      paddingTop: 0,
      borderTopWidth: 0,
    },
    line: {
      borderTopWidth: 1,
      borderTopColor: '#000000',
      height: 50,
      marginBottom: 20,
    }
  }
};

// ============================================================================
// DESIGN VARIANT REGISTRY
// ============================================================================

export const designVariants = {
  professional: professionalVariant,
  modern: modernVariant,
  minimal: minimalVariant,
} as const;

export type DesignVariantName = keyof typeof designVariants;

// ============================================================================
// DEFAULT CONFIGURATION
// ============================================================================

/**
 * Default design configuration (professional variant)
 */
export const defaultDesignConfig: ContractDesignConfig = professionalVariant;
